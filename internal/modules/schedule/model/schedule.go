package model

import (
	"time"
)

type Turn struct {
	ID        string
	Name      string
	StartTime int
	EndTime   int
	CreatedAt *time.Time
	UpdatedAt *time.Time
	DeletedAt *time.Time
}

type Schedule struct {
	ID              string
	Name            string
	SessionDuration int
	BreakDuration   int
	Turns           []Turn
	CreatedAt       *time.Time
	UpdatedAt       *time.Time
	DeletedAt       *time.Time
}

type TurnCreate struct {
	Name      string
	StartTime int
	EndTime   int
}

type TurnUpdate struct {
	ID        string
	Name      string
	StartTime int
	EndTime   int
}

type ScheduleCreate struct {
	Name            string
	SessionDuration int
	BreakDuration   int
	Turns           []TurnCreate
}

type ScheduleUpdate struct {
	ID              string
	Name            string
	SessionDuration int
	BreakDuration   int
	Turns           []TurnUpdate
}
