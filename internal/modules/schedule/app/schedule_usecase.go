package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
)

type scheduleUsecase struct {
	repo model.ScheduleRepository
}

// Delete implements model.ScheduleUsecase.
func (s *scheduleUsecase) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// GetAll implements model.ScheduleUsecase.
func (s *scheduleUsecase) GetAll(ctx context.Context) ([]model.Schedule, error) {
	return s.repo.GetAll(ctx)
}

// GetByProp implements model.ScheduleUsecase.
func (s *scheduleUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Schedule, error) {
	return s.repo.GetByProp(ctx, prop, value)
}

func NewScheduleUsecase(repo model.ScheduleRepository) model.ScheduleUsecase {
	return &scheduleUsecase{
		repo: repo,
	}
}
