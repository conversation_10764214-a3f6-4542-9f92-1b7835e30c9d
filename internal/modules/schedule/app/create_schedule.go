package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

// Create implements model.ScheduleUsecase.
func (s *scheduleUsecase) Create(ctx context.Context, scheduleCreate model.ScheduleCreate) (string, error) {
	newSchedule := model.Schedule{
		ID:              utils.UniqueId(),
		Name:            scheduleCreate.Name,
		SessionDuration: scheduleCreate.SessionDuration,
		BreakDuration:   scheduleCreate.BreakDuration,
	}

	// Create turns
	var turns []model.Turn
	for _, turnCreate := range scheduleCreate.Turns {
		turn := model.Turn{
			ID:        utils.UniqueId(),
			Name:      turnCreate.Name,
			StartTime: turnCreate.StartTime,
			EndTime:   turnCreate.EndTime,
		}
		turns = append(turns, turn)
	}

	newSchedule.Turns = turns

	err := s.repo.Create(ctx, newSchedule)
	if err != nil {
		return "", err
	}

	return newSchedule.ID, nil
}
