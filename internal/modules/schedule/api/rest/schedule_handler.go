package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ScheduleHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
}

type scheduleHandler struct {
	useCase   model.ScheduleUsecase
	validator *validator.Validate
	log       *logrus.Logger
}

func NewScheduleHandler(useCase model.ScheduleUsecase, validator *validator.Validate, log *logrus.Logger) ScheduleHandler {
	return &scheduleHandler{
		useCase:   useCase,
		validator: validator,
		log:       log,
	}
}
