package repo

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/repo/pg"
)

type scheduleRepository struct {
	pgRepo pg.SchedulePostgreRepo
}

// CountByProp implements model.ScheduleRepository.
func (s *scheduleRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return s.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.ScheduleRepository.
func (s *scheduleRepository) Create(ctx context.Context, schedule model.Schedule) error {
	return s.pgRepo.Create(ctx, schedule)
}

// Delete implements model.ScheduleRepository.
func (s *scheduleRepository) Delete(ctx context.Context, id string) error {
	return s.pgRepo.Delete(ctx, id)
}

// GetAll implements model.ScheduleRepository.
func (s *scheduleRepository) GetAll(ctx context.Context) ([]model.Schedule, error) {
	return s.pgRepo.GetAll(ctx)
}

// GetByProp implements model.ScheduleRepository.
func (s *scheduleRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Schedule, error) {
	return s.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.ScheduleRepository.
func (s *scheduleRepository) Update(ctx context.Context, schedule model.Schedule) error {
	return s.pgRepo.Update(ctx, schedule)
}

func NewScheduleRepository(pgRepo pg.SchedulePostgreRepo) model.ScheduleRepository {
	return &scheduleRepository{
		pgRepo: pgRepo,
	}
}
