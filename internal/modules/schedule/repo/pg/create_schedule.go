package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *schedulePostgreRepo) Create(ctx context.Context, schedule model.Schedule) error {
	return pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start transaction
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to begin transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// Insert schedule
		scheduleQuery := `
			INSERT INTO schedules (id, name, session_duration, break_duration)
			VALUES ($1, $2, $3, $4)
		`

		_, err = tx.Exec(ctx, scheduleQuery,
			schedule.ID,
			schedule.Name,
			schedule.SessionDuration,
			schedule.BreakDuration,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create schedule", err, nil)
		}

		// Insert turns
		for _, turn := range schedule.Turns {
			turnQuery := `
				INSERT INTO turns (id, schedule_id, name, start_time, end_time)
				VALUES ($1, $2, $3, $4, $5)
			`

			_, err = tx.Exec(ctx, turnQuery,
				turn.ID,
				schedule.ID,
				turn.Name,
				turn.StartTime,
				turn.EndTime,
			)

			if err != nil {
				return utils.InternalErrorf("failed to create turn", err, nil)
			}
		}

		// Commit transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("failed to commit transaction", err, nil)
		}

		return nil
	})
}
