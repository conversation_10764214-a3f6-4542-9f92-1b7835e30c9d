package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type SchedulePostgreRepo interface {
	Create(ctx context.Context, schedule model.Schedule) error
	Update(ctx context.Context, schedule model.Schedule) error
	GetByProp(ctx context.Context, prop string, value string) (*model.Schedule, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.Schedule, error)
	Delete(ctx context.Context, id string) error
}

type schedulePostgreRepo struct {
	pool *pgxpool.Pool
}

func NewSchedulePostgreRepo(pool *pgxpool.Pool) SchedulePostgreRepo {
	return &schedulePostgreRepo{
		pool: pool,
	}
}
