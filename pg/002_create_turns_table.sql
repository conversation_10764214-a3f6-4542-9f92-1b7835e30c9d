-- Create turns table
CREATE TABLE IF NOT EXISTS turns (
    id VARCHAR(255) PRIMARY KEY,
    schedule_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    start_time INTEGER NOT NULL,
    end_time INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign key constraint
    CONSTRAINT fk_turns_schedule_id 
        FOREIGN KEY (schedule_id) 
        REFERENCES schedules(id) 
        ON DELETE CASCADE
);

-- Create index on schedule_id for faster joins
CREATE INDEX IF NOT EXISTS idx_turns_schedule_id ON turns(schedule_id);

-- Create index on start_time for ordering
CREATE INDEX IF NOT EXISTS idx_turns_start_time ON turns(start_time);

-- Create index on deleted_at for soft delete queries
CREATE INDEX IF NOT EXISTS idx_turns_deleted_at ON turns(deleted_at);

-- Create composite index for active turns by schedule
CREATE INDEX IF NOT EXISTS idx_turns_schedule_active ON turns(schedule_id, deleted_at, start_time) WHERE deleted_at IS NULL;

-- Create constraint to ensure start_time < end_time
ALTER TABLE turns ADD CONSTRAINT chk_turns_time_order CHECK (start_time < end_time);
