-- Create schedules table
CREATE TABLE IF NOT EXISTS schedules (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    session_duration INTEGER NOT NULL,
    break_duration INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- Create index on name for faster searches
CREATE INDEX IF NOT EXISTS idx_schedules_name ON schedules(name);

-- Create index on deleted_at for soft delete queries
CREATE INDEX IF NOT EXISTS idx_schedules_deleted_at ON schedules(deleted_at);

-- Create composite index for active schedules
CREATE INDEX IF NOT EXISTS idx_schedules_active ON schedules(id, deleted_at) WHERE deleted_at IS NULL;
